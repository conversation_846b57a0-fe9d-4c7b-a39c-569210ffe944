#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
🧠 BCT - BACCARAT COUNTING TOOL
================================================================================

Outil de comptage Baccarat simplifié
VERSION REFACTORISÉE : Interface graphique + Système de comptage uniquement

COMPOSANTS OPÉRATIONNELS :
- Configuration centralisée
- Système de comptage conforme à systeme_comptage_baccarat_complet.txt
- Interface graphique fonctionnelle
- Gestion des données de partie

AUTEUR : AZR System
DATE : 2025
VERSION : 3.0.0 (BCT Refactorisé)
================================================================================
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bct.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

################################################################################
#                                                                              #
#  📋 SECTION 1 : CONFIGURATION CENTRALISÉE AZR                               #
#                                                                              #
################################################################################

class BaccaratConfig:
    """
    Configuration centralisée pour le système de comptage Baccarat

    PRINCIPE : Aucune valeur codée en dur dans les méthodes
    Configuration simplifiée sans éléments de prédiction
    """

    def __init__(self):
        # ====================================================================
        # SYSTÈME DE COMPTAGE BACCARAT (4 INDEX + 1 CALCULÉ)
        # ====================================================================

        # INDEX 1 : Comptage PAIR/IMPAIR des cartes
        self.card_count_categories = {
            'pair_4': 4,    # Aucune 3ème carte
            'pair_6': 6,    # Deux 3èmes cartes
            'impair_5': 5   # Une 3ème carte
        }

        # Brûlage possible (2-11 cartes)
        self.burn_card_range = {
            'min': 2,
            'max': 11,
            'categories': {
                'pair_2': 2, 'pair_4': 4, 'pair_6': 6, 'pair_8': 8, 'pair_10': 10,
                'impair_3': 3, 'impair_5': 5, 'impair_7': 7, 'impair_9': 9, 'impair_11': 11
            }
        }

        # INDEX 2 : États SYNC/DESYNC
        self.sync_states = ['SYNC', 'DESYNC']
        self.initial_sync_mapping = {
            'PAIR': 'SYNC',
            'IMPAIR': 'DESYNC'
        }

        # INDEX 3 : Résultats P/B/T
        self.game_results = ['PLAYER', 'BANKER', 'TIE']
        self.pb_results = ['PLAYER', 'BANKER']  # Pour calculs S/O

        # INDEX 4 : Conversions S/O
        self.so_conversions = ['S', 'O', '', 'E']  # Same, Opposite, Première manche, Égalité

        # ====================================================================
        # LIMITES ET CONTRAINTES
        # ====================================================================

        # Limites par partie
        self.max_manches_per_game = 60  # Fenêtre de 60 manches (2^60 possibilités)
        self.max_hands_per_game = 100   # Incluant TIE
        

################################################################################
#                                                                              #
#  🎯 SECTION 2 : STRUCTURES DE DONNÉES SYSTÈME COMPTAGE                      #
#                                                                              #
################################################################################

@dataclass
class BaccaratHand:
    """
    Structure de données pour une MAIN de Baccarat
    
    Conforme à systeme_comptage_baccarat_complet.txt
    Distinction claire MAIN vs MANCHE
    """
    
    # Identification
    hand_number: int
    pb_hand_number: Optional[int]       # Numéro de manche P/B (None si TIE)
    
    # INDEX 1 : Comptage cartes distribuées
    cards_distributed: int              # 4, 5, ou 6 cartes
    cards_parity: str                   # 'PAIR' ou 'IMPAIR'
    cards_category: str                 # 'pair_4', 'pair_6', 'impair_5'
    
    # INDEX 2 : État SYNC/DESYNC
    sync_state: str                     # 'SYNC' ou 'DESYNC'

    # INDEX 3 : Résultat P/B uniquement
    result: str                         # 'PLAYER', 'BANKER', 'TIE'

    # INDEX 4 : Conversion S/O
    so_conversion: str                  # 'S', 'O', '', 'E' 
    
    # Métadonnées
    timestamp: datetime = field(default_factory=datetime.now)

    @property
    def index5_combined(self) -> str:
        """
        INDEX 5 : Combiné INDEX 1 + INDEX 2
        Format : cards_category_sync_state
        Exemple : 'impair_5_DESYNC'
        """
        return f"{self.cards_category}_{self.sync_state}"

    @property
    def combined_state(self) -> str:
        """
        INDEX 6 : État combiné complet calculé automatiquement
        Combine INDEX 1 + INDEX 2 + INDEX 3 (si renseigné)

        Returns:
            str: État combiné comme 'pair_4_sync', 'impair_5_desync_banker', etc.
        """
        if self.result:  # Si INDEX 3 renseigné (pas brûlage)
            return f"{self.cards_category}_{self.sync_state.lower()}_{self.result.lower()}"
        else:  # Brûlage (INDEX 3 vide)
            return f"{self.cards_category}_{self.sync_state.lower()}"

    def is_pb_hand(self) -> bool:
        """Vérifie si c'est une manche P/B (pas TIE)"""
        return self.result in ['PLAYER', 'BANKER']

    def is_tie_hand(self) -> bool:
        """Vérifie si c'est un TIE"""
        return self.result == 'TIE'

    def is_burn_hand(self) -> bool:
        """Vérifie si c'est la main de brûlage (main 0)"""
        return self.hand_number == 0

@dataclass  
class BaccaratGame:
    """
    Structure de données pour une partie complète de Baccarat
    
    Fenêtre de 60 manches P/B maximum (2^60 possibilités)
    """
    
    # Identification
    game_number: int
    
    # Initialisation
    burn_cards_count: int               # 2-11 cartes brûlées
    burn_parity: str                    # 'PAIR' ou 'IMPAIR'
    initial_sync_state: str             # 'SYNC' ou 'DESYNC'
    
    # Données de la partie
    hands: List[BaccaratHand] = field(default_factory=list)
    
    # Statistiques
    total_hands: int = 0                # Toutes les mains (incluant TIE)
    pb_hands: int = 0                   # Manches P/B seulement
    tie_hands: int = 0                  # TIE seulement
    so_conversions: int = 0             # Conversions S/O calculées
    
    # État actuel
    current_sync_state: str = 'SYNC'
    last_pb_result: Optional[str] = None
    
    def add_hand(self, hand: BaccaratHand):
        """Ajoute une main à la partie avec mise à jour des statistiques"""
        self.hands.append(hand)

        # Ne pas compter la main 0 (brûlage) dans les statistiques
        if not hand.is_burn_hand():
            self.total_hands += 1

            if hand.result in ['PLAYER', 'BANKER']:
                self.pb_hands += 1
                if hand.so_conversion in ['S', 'O']:
                    self.so_conversions += 1
            elif hand.result == 'TIE':
                self.tie_hands += 1
    
    def is_complete(self, max_pb_hands: int = 60) -> bool:
        """Vérifie si la partie est complète (60 manches P/B)"""
        return self.pb_hands >= max_pb_hands
    
    def get_pb_sequence(self) -> List[str]:
        """Retourne la séquence P/B (sans TIE)"""
        return [hand.result for hand in self.hands if hand.result in ['PLAYER', 'BANKER']]
    
    def get_so_sequence(self) -> List[str]:
        """Retourne la séquence S/O (sans '--')"""
        return [hand.so_conversion for hand in self.hands
                if hand.so_conversion in ['S', 'O']]

    def get_sync_pattern(self) -> List[str]:
        """
        Pattern SYNC/DESYNC de toutes les mains
        """
        return [hand.sync_state for hand in self.hands]

    def get_combined_states(self) -> List[str]:
        """
        États combinés de toutes les mains
        """
        return [hand.combined_state for hand in self.hands]

    def get_parity_pattern(self) -> List[str]:
        """
        Pattern parité cartes de toutes les mains
        """
        return [hand.cards_parity for hand in self.hands]

    def get_game_summary(self) -> Dict[str, Any]:
        """
        Résumé complet de la partie pour affichage
        """
        return {
            # Séquences de base
            'pb_sequence': self.get_pb_sequence(),
            'so_sequence': self.get_so_sequence(),
            'sync_pattern': self.get_sync_pattern(),
            'combined_states': self.get_combined_states(),
            'parity_pattern': self.get_parity_pattern(),

            # Statistiques
            'total_hands': self.total_hands,
            'pb_hands': self.pb_hands,
            'tie_hands': self.tie_hands,
            'last_pb_result': self.last_pb_result,
            'current_sync_state': self.current_sync_state
        }

################################################################################
#                                                                              #
#  ⚙️ SECTION 3 : MOTEUR DE COMPTAGE OPÉRATIONNEL                              #
#                                                                              #
################################################################################

class BaccaratCountingEngine:
    """
    Moteur de comptage conforme à systeme_comptage_baccarat_complet.txt

    Calcule les 4 INDEX de comptage + INDEX 6 combiné pour chaque main
    OPÉRATIONNEL et TESTÉ
    """

    def __init__(self, config: BaccaratConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.CountingEngine")

    def calculate_cards_distributed(self, total_cards: int, cards_category: str = None) -> Tuple[int, str, str]:
        """
        Calcule INDEX 1 : nombre de cartes distribuées et catégorie

        Args:
            total_cards: Nombre total de cartes distribuées (4, 5, ou 6)
            cards_category: Catégorie pré-calculée (optionnel)

        Returns:
            Tuple[total_cards, parity, category]
        """
        if total_cards % 2 == 0:
            parity = 'PAIR'
            if total_cards == 4:
                category = 'pair_4'
            elif total_cards == 6:
                category = 'pair_6'
            else:
                # Cas exceptionnels (ne devrait pas arriver pour les mains normales)
                category = f'pair_{total_cards}'
        else:
            parity = 'IMPAIR'
            if total_cards == 5:
                category = 'impair_5'
            else:
                # Cas exceptionnels
                category = f'impair_{total_cards}'

        # Utiliser la catégorie pré-calculée si fournie
        if cards_category:
            category = cards_category

        return total_cards, parity, category

    def create_burn_hand(self, burn_cards_count: int, burn_parity: str, initial_sync_state: str) -> BaccaratHand:
        """
        Crée la main 0 (brûlage) avec seulement INDEX 1 et INDEX 2 renseignés

        Args:
            burn_cards_count: Nombre de cartes brûlées (2-11)
            burn_parity: Parité du brûlage ('PAIR' ou 'IMPAIR')
            initial_sync_state: État initial ('SYNC' ou 'DESYNC')

        Returns:
            BaccaratHand: Main 0 avec INDEX 1&2 seulement
        """
        # INDEX 1 : Catégorie de brûlage selon le nombre de cartes
        if burn_parity == 'PAIR':
            if burn_cards_count in [2, 4, 6, 8, 10]:
                cards_category = f'pair_{burn_cards_count}'
            else:
                cards_category = 'pair_4'  # Défaut si nombre invalide
        else:  # IMPAIR
            if burn_cards_count in [3, 5, 7, 9, 11]:
                cards_category = f'impair_{burn_cards_count}'
            else:
                cards_category = 'impair_5'  # Défaut si nombre invalide

        # Créer la main 0 (brûlage)
        burn_hand = BaccaratHand(
            hand_number=0,                    # Main 0
            pb_hand_number=0,                 # Manche 0 (brûlage)
            cards_distributed=burn_cards_count,  # Nombre de cartes brûlées
            cards_parity=burn_parity,         # INDEX 1 : Parité
            cards_category=cards_category,    # INDEX 1 : Catégorie
            sync_state=initial_sync_state,    # INDEX 2 : État initial
            result='',                        # INDEX 3 : Vide
            so_conversion=''                  # INDEX 4 : Vide
        )

        self.logger.debug(f"Main de brûlage créée: {cards_category}_{initial_sync_state}")
        return burn_hand

    def calculate_sync_state(self, current_sync_state: str, cards_parity: str) -> str:
        """
        Calcule INDEX 2 : nouvel état SYNC/DESYNC

        LOGIQUE :
        - Nombre PAIR de cartes → CONSERVE l'état
        - Nombre IMPAIR de cartes → CHANGE l'état
        """
        if cards_parity == 'PAIR':
            # PAIR conserve l'état
            return current_sync_state
        else:
            # IMPAIR change l'état
            return 'DESYNC' if current_sync_state == 'SYNC' else 'SYNC'

    def calculate_so_conversion(self, current_result: str, last_pb_result: Optional[str]) -> str:
        """
        Calcule INDEX 4 : conversion S/O

        LOGIQUE :
        - S (Same) : Même résultat que la dernière manche P/B
        - O (Opposite) : Résultat opposé à la dernière manche P/B
        - -- : Première manche P/B
        - E (Egalité) : Pour les TIE
        """
        if current_result == 'TIE':
            return 'E'  # TIE n'a pas de conversion S/O

        if last_pb_result is None:
            return '--'  # Première manche P/B

        if current_result == last_pb_result:
            return 'S'   # Same
        else:
            return 'O'   # Opposite

    def process_hand(self, game: BaccaratGame, result: str,
                    total_cards: int, cards_category: str) -> BaccaratHand:
        """
        Traite une main complète et calcule tous les index

        Args:
            game: Partie en cours
            result: 'PLAYER', 'BANKER', 'TIE'
            total_cards: Nombre total de cartes distribuées (4, 5, ou 6)
            cards_category: Catégorie pré-calculée ('pair_4', 'impair_5', 'pair_6')
        """
        # INDEX 1 : Comptage cartes
        total_cards, cards_parity, cards_category = self.calculate_cards_distributed(
            total_cards, cards_category
        )

        # INDEX 2 : Nouvel état SYNC/DESYNC
        new_sync_state = self.calculate_sync_state(
            game.current_sync_state, cards_parity
        )

        # INDEX 4 : Conversion S/O
        so_conversion = self.calculate_so_conversion(result, game.last_pb_result)

        # Numéro de manche selon la logique correcte
        pb_hand_number = None
        if result in ['PLAYER', 'BANKER']:
            # P/B : incrémente le compteur de manches
            pb_hand_number = game.pb_hands + 1
        elif result == 'TIE':
            # TIE : garde le numéro de la dernière manche P/B (MÊME manche)
            if game.pb_hands > 0:
                pb_hand_number = game.pb_hands  # MÊME numéro que la dernière manche P/B
            else:
                pb_hand_number = 0  # Si pas encore de P/B, reste à 0 (comme brûlage)

        # Créer la main (numérotation commence à 1 car main 0 = brûlage)
        # Si main 0 (brûlage) existe, les mains suivantes sont 1, 2, 3...
        hand_number = len(game.hands)  # game.hands[0] = brûlage, donc len = 1 → main 1

        # INDEX 3 et 4 : Gestion correcte selon les règles
        if result == 'TIE':
            result_index3 = 'TIE'  # INDEX 3 contient 'TIE'
            so_conversion_index4 = 'E'  # INDEX 4 contient 'E' pour TIE
        elif game.pb_hands == 0:  # Première manche P/B (main 1)
            result_index3 = result  # INDEX 3 contient le résultat (PLAYER/BANKER)
            so_conversion_index4 = ''  # INDEX 4 vide pour première manche P/B
        else:
            result_index3 = result  # INDEX 3 normal pour manches P/B suivantes
            so_conversion_index4 = so_conversion  # INDEX 4 normal (S/O)

        hand = BaccaratHand(
            hand_number=hand_number,
            pb_hand_number=pb_hand_number,
            cards_distributed=total_cards,
            cards_parity=cards_parity,
            cards_category=cards_category,
            sync_state=new_sync_state,
            result=result_index3,
            so_conversion=so_conversion_index4
        )

        # Mettre à jour l'état du jeu
        game.current_sync_state = new_sync_state
        if result in ['PLAYER', 'BANKER']:
            game.last_pb_result = result

        # Ajouter la main au jeu
        game.add_hand(hand)

        self.logger.info(f"Main traitée: {result} {hand.cards_distributed} cartes -> INDEX5: {hand.index5_combined} | INDEX6: {hand.combined_state} | INDEX4: {hand.so_conversion}")

        return hand

################################################################################
#                                                                              #
#  🎮 SECTION 4 : INTERFACE GRAPHIQUE OPÉRATIONNELLE                          #
#                                                                              #
################################################################################

class BaccaratInterface:
    """
    Interface graphique avec 9 boutons (3 résultats × 3 nombres de cartes)
    Version simplifiée sans prédictions
    """

    def __init__(self, config: BaccaratConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.Interface")

        # Composants système
        self.counting_engine = BaccaratCountingEngine(config)

        # État de l'interface
        self.current_game = None
        self.burn_initialized = False

        # Interface graphique
        self.root = tk.Tk()
        self.root.title("🧠 BCT - Baccarat Counting Tool")
        self.root.geometry("1000x700")

        # Variables d'affichage
        self.game_stats_var = tk.StringVar(value="Partie: 0/60")

        self._create_interface()

        self.logger.info("Interface graphique initialisée")

    def _create_interface(self):
        """Crée l'interface graphique complète"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Section initialisation brûlage
        self._create_burn_section(main_frame)

        # Section statut de la partie
        self._create_game_status_display(main_frame)

        # Section 9 boutons (3×3)
        self._create_nine_buttons_section(main_frame)

        # Section contrôles
        self._create_controls_section(main_frame)

        # Section statistiques
        self._create_stats_section(main_frame)

    def _create_burn_section(self, parent):
        """Crée la section d'initialisation du brûlage"""
        burn_frame = ttk.LabelFrame(parent, text="🔥 Initialisation", padding="10")
        burn_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(burn_frame, text="Brûlage:", font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        # Seulement 2 boutons : PAIR et IMPAIR
        buttons_frame = ttk.Frame(burn_frame)
        buttons_frame.pack(side=tk.LEFT, padx=(20, 0))

        # Bouton PAIR - fond noir, police jaune, plus petit
        pair_btn = tk.Button(buttons_frame, text="PAIR",
                            font=("Arial", 10, "bold"),
                            bg="black", fg="yellow",
                            width=8, height=1,
                            command=lambda: self._initialize_burn('PAIR'))
        pair_btn.pack(side=tk.LEFT, padx=5)

        # Bouton IMPAIR - fond noir, police jaune, plus petit
        impair_btn = tk.Button(buttons_frame, text="IMPAIR",
                              font=("Arial", 10, "bold"),
                              bg="black", fg="yellow",
                              width=8, height=1,
                              command=lambda: self._initialize_burn('IMPAIR'))
        impair_btn.pack(side=tk.LEFT, padx=5)

    def _create_game_status_display(self, parent):
        """Crée la section d'affichage du statut de la partie"""
        status_frame = ttk.LabelFrame(parent, text="📊 Statut de la Partie", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        # Statistiques partie - avec formatage personnalisé
        stats_frame = ttk.Frame(status_frame)
        stats_frame.pack(fill=tk.X)

        # Frame pour les statistiques avec formatage mixte
        self.stats_display_frame = ttk.Frame(stats_frame)
        self.stats_display_frame.pack(side=tk.LEFT)

        # Labels séparés pour formatage différent
        self.manche_label = ttk.Label(self.stats_display_frame, text="Manche : ", font=("Arial", 12))
        self.manche_label.pack(side=tk.LEFT)

        self.manche_numbers = ttk.Label(self.stats_display_frame, text="0 / 60", font=("Arial", 12, "bold"))
        self.manche_numbers.pack(side=tk.LEFT)

        self.other_stats = ttk.Label(self.stats_display_frame, text="", font=("Arial", 12))
        self.other_stats.pack(side=tk.LEFT)

    def _create_nine_buttons_section(self, parent):
        """
        Crée la section des 9 boutons (3 résultats × 3 nombres de cartes)
        AMÉLIORATIONS INTERFACE :
        - Suppression des étiquettes headers inutiles
        - Réduction taille boutons par 2
        - Police plus claire pour meilleure lisibilité
        """
        buttons_frame = ttk.LabelFrame(parent, text="🎲 Saisie Manches (Résultat + Nombre de cartes)", padding="15")
        buttons_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Configuration grid
        for i in range(3):
            buttons_frame.columnconfigure(i, weight=1)

        # Boutons pour chaque combinaison (résultat + nombre de cartes)
        # SUPPRESSION DES HEADERS INUTILES - directement les boutons
        headers = ["PLAYER", "BANKER", "TIE"]
        colors = ["#1E3A8A", "#B91C1C", "#166534"]  # Bleu, Rouge, Vert
        card_counts = [
            (4, "4 cartes totales"),
            (5, "5 cartes totales"),
            (6, "6 cartes totales")
        ]

        for row, (total_cards, card_desc) in enumerate(card_counts, start=0):  # start=0 car plus de headers
            for col, (result, color) in enumerate(zip(headers, colors)):
                btn_text = f"{result} {total_cards}\n({card_desc})"

                btn = tk.Button(buttons_frame, text=btn_text,
                              font=("Arial", 8, "bold"),  # Police plus petite mais lisible
                              bg=color, fg="#F0F0F0",      # Couleur police plus claire
                              relief="raised", bd=2,
                              height=2, width=12,          # Taille réduite par 2
                              command=lambda r=result, c=total_cards: self._process_hand(r, c))
                btn.grid(row=row, column=col, sticky="ew", padx=2, pady=2)

    def _create_controls_section(self, parent):
        """Crée la section des contrôles"""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, pady=(0, 10))

        # Boutons de contrôle
        ttk.Button(controls_frame, text="💾 Sauvegarder",
                  command=self._save_game).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(controls_frame, text="🔄 Nouvelle Partie",
                  command=self._new_game).pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="📊 Statistiques",
                  command=self._show_statistics).pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="❌ Quitter",
                  command=self._quit_application).pack(side=tk.RIGHT)

    def _create_stats_section(self, parent):
        """Crée la section des statistiques en temps réel"""
        stats_frame = ttk.LabelFrame(parent, text="📈 Statistiques Temps Réel", padding="10")
        stats_frame.pack(fill=tk.X)

        # Créer un Text widget pour affichage des stats
        self.stats_text = tk.Text(stats_frame, height=6, width=80,
                                 font=("Courier", 9), state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stats_text.config(yscrollcommand=scrollbar.set)

    def _initialize_burn(self, parity: str):
        """Initialise le brûlage avec la parité ET crée la main 0"""
        if self.burn_initialized:
            messagebox.showwarning("Attention", "Brûlage déjà initialisé pour cette partie")
            return

        # Déterminer état initial et nombre de cartes par défaut
        initial_sync_state = self.config.initial_sync_mapping[parity]
        burn_cards_count = 4 if parity == 'PAIR' else 5  # Défaut : pair_4 ou impair_5

        # Créer nouvelle partie (initialisation)
        self.current_game = BaccaratGame(
            game_number=1,
            burn_cards_count=burn_cards_count,
            burn_parity=parity,
            initial_sync_state=initial_sync_state,
            current_sync_state=initial_sync_state
        )

        # Créer la main 0 (brûlage) avec seulement INDEX 1 et INDEX 2
        burn_hand = self.counting_engine.create_burn_hand(
            burn_cards_count, parity, initial_sync_state
        )

        # Ajouter la main 0 au jeu
        self.current_game.add_hand(burn_hand)

        self.burn_initialized = True

        # Mettre à jour affichage
        self._update_display()

        self.logger.info(f"Brûlage initialisé: parité {parity} -> {initial_sync_state}")
        self.logger.info(f"Main 0 créée: {burn_hand.cards_category}_{burn_hand.sync_state}")
        messagebox.showinfo("Brûlage Initialisé",
                          f"Brûlage: parité {parity}\nÉtat initial: {initial_sync_state}\nMain 0 créée: {burn_hand.cards_category}\n\nVous pouvez maintenant saisir les manches.")

    def _process_hand(self, result: str, total_cards: int):
        """
        Traite une main avec résultat et nombre total de cartes

        Args:
            result: 'PLAYER', 'BANKER', 'TIE' (le gagnant de la main)
            total_cards: 4, 5, ou 6 (nombre total de cartes distribuées dans la main)
        """
        if not self.burn_initialized:
            messagebox.showwarning("Attention", "Veuillez d'abord initialiser le brûlage")
            return

        if self.current_game.is_complete():
            messagebox.showinfo("Partie Terminée",
                              f"Partie complète: {self.config.max_manches_per_game} manches P/B atteintes")
            return

        try:
            # Mapping automatique vers catégorie INDEX 1
            category_mapping = {
                4: 'pair_4',     # 4 cartes totales = pair_4
                5: 'impair_5',   # 5 cartes totales = impair_5
                6: 'pair_6'      # 6 cartes totales = pair_6
            }

            cards_category = category_mapping.get(total_cards)
            if not cards_category:
                raise ValueError(f"Nombre de cartes invalide: {total_cards}")

            # Traiter la main avec le moteur de comptage
            # Le moteur calcule automatiquement tous les INDEX
            hand = self.counting_engine.process_hand(
                self.current_game, result, total_cards, cards_category
            )

            # Mettre à jour affichage
            self._update_display()

            # Log de la main traitée avec TOUS les INDEX
            self.logger.info(f"Main traitée: {result} {total_cards} cartes -> {hand.cards_category}_{hand.sync_state}_{hand.result} {hand.so_conversion}")

        except Exception as e:
            self.logger.error(f"Erreur traitement main: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du traitement: {e}")

    def _update_display(self):
        """Met à jour l'affichage de l'interface"""
        if not self.current_game:
            return

        try:
            # Mettre à jour statistiques de partie avec formatage personnalisé
            self.manche_numbers.config(text=f"{self.current_game.pb_hands} / {self.config.max_manches_per_game}")
            self.other_stats.config(text=f" | Total: {self.current_game.total_hands} | TIE: {self.current_game.tie_hands}")

            # Mettre à jour les statistiques détaillées (avec protection)
            self.root.after_idle(self._update_stats_display)
        except Exception as e:
            self.logger.error(f"Erreur mise à jour affichage: {e}")

    def _update_stats_display(self):
        """Met à jour l'affichage des statistiques détaillées"""
        if not self.current_game:
            return

        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)

        # Affichage système de comptage
        self.stats_text.insert(tk.END, f"⚙️ SYSTÈME DE COMPTAGE:\n")
        self.stats_text.insert(tk.END, f"  • État SYNC/DESYNC: {self.current_game.current_sync_state}\n")
        self.stats_text.insert(tk.END, f"  • Dernière main P/B: {self.current_game.last_pb_result or 'Aucune'}\n")

        if self.current_game.hands:
            last_hand = self.current_game.hands[-1]
            self.stats_text.insert(tk.END, f"\n📋 DERNIÈRE MAIN TRAITÉE:\n")
            self.stats_text.insert(tk.END, f"  • Résultat: {last_hand.result or 'TIE'}\n")
            self.stats_text.insert(tk.END, f"  • Cartes: {last_hand.cards_distributed} ({last_hand.cards_category})\n")
            self.stats_text.insert(tk.END, f"  • État SYNC: {last_hand.sync_state}\n")
            self.stats_text.insert(tk.END, f"  • INDEX 5 (INDEX 1+2): {last_hand.index5_combined}\n")
            self.stats_text.insert(tk.END, f"  • INDEX 6 (combiné): {last_hand.combined_state}\n")
            self.stats_text.insert(tk.END, f"  • Conversion S/O: {last_hand.so_conversion or 'N/A'}\n")

        self.stats_text.insert(tk.END, f"\n📊 SÉQUENCES:\n")
        pb_sequence = self.current_game.get_pb_sequence()
        so_sequence = self.current_game.get_so_sequence()
        sync_pattern = self.current_game.get_sync_pattern()

        self.stats_text.insert(tk.END, f"  • P/B: {' '.join(pb_sequence[-10:]) if pb_sequence else 'Aucune'}\n")
        self.stats_text.insert(tk.END, f"  • S/O: {' '.join(so_sequence[-10:]) if so_sequence else 'Aucune'}\n")
        self.stats_text.insert(tk.END, f"  • SYNC: {' '.join(sync_pattern[-10:]) if sync_pattern else 'Aucune'}\n")

        self.stats_text.config(state=tk.DISABLED)
        self.stats_text.see(tk.END)



    def _save_game(self):
        """Sauvegarde la partie actuelle"""
        if not self.current_game:
            messagebox.showwarning("Attention", "Aucune partie en cours")
            return

        try:
            # Créer données de sauvegarde
            save_data = {
                'game': {
                    'game_number': self.current_game.game_number,
                    'burn_cards_count': self.current_game.burn_cards_count,
                    'burn_parity': self.current_game.burn_parity,
                    'initial_sync_state': self.current_game.initial_sync_state,
                    'hands': [
                        {
                            'hand_number': hand.hand_number,
                            'pb_hand_number': hand.pb_hand_number,
                            'cards_distributed': hand.cards_distributed,
                            'cards_parity': hand.cards_parity,
                            'cards_category': hand.cards_category,
                            'sync_state': hand.sync_state,
                            'combined_state': hand.combined_state,
                            'result': hand.result,
                            'so_conversion': hand.so_conversion,
                            'timestamp': hand.timestamp.isoformat()
                        }
                        for hand in self.current_game.hands
                    ]
                },
                'save_timestamp': datetime.now().isoformat()
            }

            # Sauvegarder dans fichier JSON
            filename = f"bct_game_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("Sauvegarde", f"Partie sauvegardée: {filename}")
            self.logger.info(f"Partie sauvegardée: {filename}")

        except Exception as e:
            self.logger.error(f"Erreur sauvegarde: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _new_game(self):
        """Démarre une nouvelle partie"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Nouvelle Partie",
                                     "Voulez-vous vraiment démarrer une nouvelle partie ?\n"
                                     "La partie actuelle sera perdue si non sauvegardée."):
                return

        # Réinitialiser état
        self.current_game = None
        self.burn_initialized = False

        # Réinitialiser affichage
        self.manche_numbers.config(text="0 / 60")
        self.other_stats.config(text=" | Total: 0 | TIE: 0")

        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, "🆕 Nouvelle partie initialisée\n")
        self.stats_text.insert(tk.END, "Veuillez initialiser le brûlage pour commencer\n")
        self.stats_text.config(state=tk.DISABLED)

        self.logger.info("Nouvelle partie initialisée")

    def _show_statistics(self):
        """Affiche les statistiques détaillées dans une fenêtre séparée"""
        stats_window = tk.Toplevel(self.root)
        stats_window.title("📊 Statistiques Détaillées BCT")
        stats_window.geometry("800x600")

        # Text widget avec scrollbar
        text_frame = ttk.Frame(stats_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        stats_text = tk.Text(text_frame, font=("Courier", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=stats_text.yview)

        stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        stats_text.config(yscrollcommand=scrollbar.set)

        # Générer statistiques complètes
        stats_content = "🧠 STATISTIQUES BCT (Baccarat Counting Tool)\n"
        stats_content += "=" * 60 + "\n\n"

        # Configuration système
        stats_content += "📊 CONFIGURATION SYSTÈME:\n"
        stats_content += f"  • Manches max par partie: {self.config.max_manches_per_game}\n"
        stats_content += f"  • Mains max par partie: {self.config.max_hands_per_game}\n\n"

        # Partie actuelle
        if self.current_game:
            stats_content += f"🎲 PARTIE ACTUELLE:\n"
            stats_content += f"  • Numéro: {self.current_game.game_number}\n"
            stats_content += f"  • Brûlage: parité {self.current_game.burn_parity}\n"
            stats_content += f"  • État initial: {self.current_game.initial_sync_state}\n"
            stats_content += f"  • État actuel: {self.current_game.current_sync_state}\n"
            stats_content += f"  • Manches P/B: {self.current_game.pb_hands}/{self.config.max_manches_per_game}\n"
            stats_content += f"  • Total mains: {self.current_game.total_hands}\n"
            stats_content += f"  • TIE: {self.current_game.tie_hands}\n"
            stats_content += f"  • Conversions S/O: {self.current_game.so_conversions}\n\n"

            # Détails des mains
            if self.current_game.hands:
                stats_content += f"📋 DÉTAILS DES MAINS:\n"
                for hand in self.current_game.hands[-5:]:  # 5 dernières mains
                    stats_content += f"  Main {hand.hand_number}: {hand.result} {hand.cards_distributed}c → {hand.combined_state} ({hand.so_conversion})\n"
        else:
            stats_content += "🎲 AUCUNE PARTIE EN COURS\n"

        stats_text.insert(tk.END, stats_content)
        stats_text.config(state=tk.DISABLED)

    def _quit_application(self):
        """Quitte l'application proprement"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Quitter",
                                     "Voulez-vous vraiment quitter ?\n"
                                     "La partie actuelle sera perdue si non sauvegardée."):
                return

        self.logger.info("Fermeture de l'application")

        # Fermer interface
        self.root.quit()
        self.root.destroy()

    def run(self):
        """Lance l'interface graphique"""
        self.logger.info("Démarrage de l'interface graphique")

        # Gestionnaire de fermeture
        self.root.protocol("WM_DELETE_WINDOW", self._quit_application)

        # Démarrer boucle principale
        self.root.mainloop()

################################################################################
#                                                                              #
#  🚀 SECTION 5 : FONCTION PRINCIPALE                                         #
#                                                                              #
################################################################################

def main():
    """
    Fonction principale du programme BCT (Baccarat Counting Tool)

    Initialise tous les composants et lance l'interface graphique
    """
    print("🧠 BCT - BACCARAT COUNTING TOOL (Version Refactorisée)")
    print("=" * 60)
    print("Initialisation du système...")

    try:
        # Initialiser configuration
        config = BaccaratConfig()

        print(f"\n📊 Configuration système:")
        print(f"  • Manches max par partie: {config.max_manches_per_game}")
        print(f"  • Mains max par partie: {config.max_hands_per_game}")

        print(f"\n✅ Composants opérationnels:")
        print(f"  • Configuration centralisée")
        print(f"  • Système de comptage conforme")
        print(f"  • Interface graphique simplifiée")
        print(f"  • Gestion des données de partie")

        # Initialiser et lancer interface
        print("\n🎮 Lancement de l'interface graphique...")
        interface = BaccaratInterface(config)
        interface.run()

    except KeyboardInterrupt:
        print("\n⚠️ Interruption utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        logger.error(f"Erreur fatale: {e}", exc_info=True)
    finally:
        print("\n👋 Arrêt du programme")

if __name__ == "__main__":
    main()
