            # Contexte pour prédictions
            'recent_influences': self.get_full_influence_sequence()[-10:],  # 10 dernières
            'recent_sync_pattern': self.get_sync_pattern_complete()[-10:],
            'recent_combined_states': self.get_combined_states_all()[-10:]

    def add_hand(self, hand: BaccaratHand):
        """Ajoute une main à la partie avec mise à jour des statistiques"""
        self.hands.append(hand)

        # Ne pas compter la main 0 (brûlage) dans les statistiques
        if not hand.is_burn_hand():
            self.total_hands += 1

        # Montrer exploitation TIE
        tie_influences = [inf for inf in algorithmic_data['full_influences'] if inf['is_tie']]
        if tie_influences:
            self.stats_text.insert(tk.END, f"\n📊 TIE EXPLOITÉS POUR PRÉDICTIONS:\n")
            for tie in tie_influences[-3:]:  # 3 derniers TIE
                self.stats_text.insert(tk.END, f"  • Main {tie['hand_number']}: {tie['combined_state']} (INDEX 1&2 exploités)\n")