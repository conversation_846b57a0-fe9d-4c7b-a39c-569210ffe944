#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
🧠 BCT - BACCARAT COUNTING TOOL
================================================================================

Outil de comptage Baccarat simplifié
VERSION REFACTORISÉE : Interface graphique + Système de comptage uniquement

COMPOSANTS OPÉRATIONNELS :
- Configuration centralisée
- Système de comptage conforme à systeme_comptage_baccarat_complet.txt
- Interface graphique fonctionnelle
- Gestion des données de partie

AUTEUR : AZR System
DATE : 2025
VERSION : 3.0.0 (BCT Refactorisé)
================================================================================
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox

# Configuration du logging (sera mise à jour avec AZRConfig)
def setup_logging(log_file='bct.log'):
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

# Configuration temporaire
setup_logging()
logger = logging.getLogger(__name__)

################################################################################
#                                                                              #
#  📋 SECTION 1 : CONFIGURATION CENTRALISÉE AZR                               #
#                                                                              #
################################################################################

class AZRConfig:
    """
    Configuration centralisée AZR pour le système de comptage Baccarat

    PRINCIPE : AUCUNE valeur codée en dur dans les méthodes
    Toutes les constantes, valeurs et paramètres sont centralisés ici
    """

    def __init__(self):
        # ====================================================================
        # SYSTÈME DE COMPTAGE BACCARAT (INDEX 1, 2, 3, 5)
        # ====================================================================

        # INDEX 2 : Comptage PAIR/IMPAIR des cartes
        self.card_count_categories = {
            'pair_4': 4,    # Aucune 3ème carte
            'pair_6': 6,    # Deux 3èmes cartes
            'impair_5': 5   # Une 3ème carte
        }

        # Valeurs numériques pour calculs
        self.card_counts = {
            'min': 4,
            'max': 6,
            'pair_values': [4, 6],
            'impair_values': [5]
        }

        # Brûlage possible (2-11 cartes)
        self.burn_card_range = {
            'min': 2,
            'max': 11,
            'categories': {
                'pair_2': 2, 'pair_4': 4, 'pair_6': 6, 'pair_8': 8, 'pair_10': 10,
                'impair_3': 3, 'impair_5': 5, 'impair_7': 7, 'impair_9': 9, 'impair_11': 11
            }
        }

        # Valeurs par défaut pour brûlage
        self.burn_defaults = {
            'PAIR': 4,      # pair_4 par défaut
            'IMPAIR': 5     # impair_5 par défaut
        }

        # INDEX 1 : États SYNC/DESYNC
        self.sync_states = ['SYNC', 'DESYNC']
        self.initial_sync_mapping = {
            'PAIR': 'SYNC',
            'IMPAIR': 'DESYNC'
        }

        # INDEX 3 : Résultats P/B/T
        self.game_results = ['PLAYER', 'BANKER', 'TIE']
        self.pb_results = ['PLAYER', 'BANKER']  # Pour calculs P/B

        # Parités
        self.parities = ['PAIR', 'IMPAIR']

        # ====================================================================
        # LIMITES ET CONTRAINTES
        # ====================================================================

        # Limites par partie
        self.max_manches_per_game = 60  # Fenêtre de 60 manches (2^60 possibilités)
        self.max_hands_per_game = 100   # Incluant TIE

        # Valeurs spéciales
        self.burn_hand_number = 0       # Numéro de la main de brûlage
        self.burn_pb_hand_number = 0    # Numéro de manche pour brûlage
        self.first_game_number = 1      # Numéro de la première partie

        # ====================================================================
        # INTERFACE GRAPHIQUE
        # ====================================================================

        # Dimensions fenêtre principale
        self.window_geometry = "1000x700"

        # Couleurs interface
        self.colors = {
            'player': "#1E3A8A",    # Bleu
            'banker': "#B91C1C",    # Rouge
            'tie': "#166534",       # Vert
            'burn_bg': "black",     # Fond boutons brûlage
            'burn_fg': "yellow",    # Texte boutons brûlage
            'text_light': "#F0F0F0" # Texte clair
        }

        # Polices
        self.fonts = {
            'title': ("Arial", 12, "bold"),
            'normal': ("Arial", 12),
            'burn_button': ("Arial", 10, "bold"),
            'game_button': ("Arial", 8, "bold"),
            'stats': ("Courier", 9),
            'stats_window': ("Courier", 10)
        }

        # Tailles boutons
        self.button_sizes = {
            'burn_width': 8,
            'burn_height': 1,
            'game_width': 12,
            'game_height': 2,
            'game_bd': 2,
            'control_width': 12,
            'control_height': 1
        }

        # Espacements
        self.paddings = {
            'main_frame': "10",
            'section': "10",
            'section_y': (0, 10),
            'burn_buttons': (20, 0),
            'burn_button': 5,
            'game_button': 2,
            'control_button': 5,
            'stats_window': 10
        }

        # Dimensions widgets
        self.widget_sizes = {
            'stats_height': 6,
            'stats_width': 80,
            'stats_window_width': 800,
            'stats_window_height': 600,
            'grid_columns': 3
        }

        # ====================================================================
        # TEXTES ET MESSAGES
        # ====================================================================

        # Titres sections
        self.section_titles = {
            'burn': "🔥 Initialisation",
            'status': "📊 Statut de la Partie",
            'buttons': "🎲 Saisie Manches (Résultat + Nombre de cartes)",
            'stats': "📈 Statistiques Temps Réel",
            'stats_window': "📊 Statistiques Détaillées BCT"
        }

        # Labels
        self.labels = {
            'burn': "Brûlage:",
            'manche': "Manche : ",
            'window_title': "🧠 BCT - Baccarat Counting Tool"
        }

        # Messages
        self.messages = {
            'burn_already_init': "Brûlage déjà initialisé pour cette partie",
            'burn_first': "Veuillez d'abord initialiser le brûlage",
            'game_complete': "Partie complète: {} manches P/B atteintes",
            'no_game': "Aucune partie en cours",
            'new_game_confirm': "Voulez-vous vraiment démarrer une nouvelle partie ?\nLa partie actuelle sera perdue si non sauvegardée.",
            'quit_confirm': "Voulez-vous vraiment quitter ?\nLa partie actuelle sera perdue si non sauvegardée.",
            'invalid_cards': "Nombre de cartes invalide: {}",
            'game_saved': "Partie sauvegardée: {}",
            'new_game_init': "🆕 Nouvelle partie initialisée\nVeuillez initialiser le brûlage pour commencer\n",
            'reset_confirm': "Voulez-vous vraiment réinitialiser la partie ?\nToutes les données actuelles seront perdues (y compris le brûlage).",
            'reset_success': "🔄 Partie réinitialisée\nTous les compteurs remis à zéro\nVeuillez initialiser le brûlage pour recommencer\n",
            'undo_confirm': "Voulez-vous vraiment annuler la dernière main ?\nCette action supprimera la main {} et restaurera l'état précédent.",
            'undo_success': "↩️ Main {} annulée\nÉtat restauré à la main précédente\n",
            'undo_no_hand': "Aucune main à annuler.\nSeul le brûlage est présent.",
            'undo_only_burn': "Impossible d'annuler le brûlage.\nUtilisez 'Réinitialiser' pour recommencer."
        }

        # Boutons
        self.button_texts = {
            'pair': "PAIR",
            'impair': "IMPAIR",
            'save': "💾 Sauvegarder",
            'new_game': "🔄 Nouvelle Partie",
            'statistics': "📊 Statistiques",
            'quit': "❌ Quitter",
            'reset_game': "🔄 Réinitialiser",
            'undo_hand': "↩️ Annuler Main"
        }

        # ====================================================================
        # FICHIERS ET LOGGING
        # ====================================================================

        # Fichiers
        self.files = {
            'log': 'bct.log',
            'save_prefix': 'bct_game_',
            'save_extension': '.json',
            'encoding': 'utf-8'
        }

        # Formats
        self.formats = {
            'datetime_file': '%Y%m%d_%H%M%S',
            'json_indent': 2,
            'stats_separator': "=" * 60,
            'sequence_display_limit': 10,
            'recent_hands_limit': 5
        }

        # ====================================================================
        # MAPPING AUTOMATIQUE
        # ====================================================================

        # Mapping nombre de cartes → catégorie
        self.card_mapping = {
            4: 'pair_4',
            5: 'impair_5',
            6: 'pair_6'
        }

        # Descriptions cartes
        self.card_descriptions = {
            4: "4 cartes totales",
            5: "5 cartes totales",
            6: "6 cartes totales"
        }

        # Valeurs par défaut
        self.defaults = {
            'game_stats': "Partie: 0/60",
            'manche_display': "0 / 60",
            'empty_stats': " | TIE: 0",
            'default_sync': 'SYNC',
            'empty_string': '',
            'no_result': 'Aucune',
            'na_display': 'N/A'
        }
        

################################################################################
#                                                                              #
#  🎯 SECTION 2 : STRUCTURES DE DONNÉES SYSTÈME COMPTAGE                      #
#                                                                              #
################################################################################

@dataclass
class BaccaratHand:
    """
    Structure de données pour une MAIN de Baccarat
    
    Conforme à systeme_comptage_baccarat_complet.txt
    Distinction claire MAIN vs MANCHE
    """
    
    # Identification
    hand_number: int
    pb_hand_number: Optional[int]       # Numéro de manche P/B (None si TIE)

    # INDEX 1 : État SYNC/DESYNC
    sync_state: str                     # 'SYNC' ou 'DESYNC'

    # INDEX 2 : Comptage cartes distribuées
    cards_distributed: int              # 4, 5, ou 6 cartes
    cards_parity: str                   # 'PAIR' ou 'IMPAIR'
    cards_category: str                 # 'pair_4', 'pair_6', 'impair_5'

    # INDEX 3 : Résultat P/B uniquement
    result: str                         # 'PLAYER', 'BANKER', 'TIE'


    
    # Métadonnées
    timestamp: datetime = field(default_factory=datetime.now)

    @property
    def index5_combined(self) -> str:
        """
        INDEX 5 : Combiné INDEX 1 + INDEX 2
        Format : sync_state_cards_category
        Exemple : 'SYNC_pair_6'
        """
        if self.cards_category:  # Si INDEX 2 renseigné (pas brûlage)
            return f"{self.sync_state}_{self.cards_category}"
        else:  # Brûlage (INDEX 2 vide)
            return f"{self.sync_state}"



    def is_pb_hand(self, config: AZRConfig = None) -> bool:
        """Vérifie si c'est une manche P/B (pas TIE)"""
        if config:
            return self.result in config.pb_results
        return self.result in ['PLAYER', 'BANKER']  # Fallback

    def is_tie_hand(self, config: AZRConfig = None) -> bool:
        """Vérifie si c'est un TIE"""
        if config:
            return self.result == config.game_results[2]  # 'TIE'
        return self.result == 'TIE'  # Fallback

    def is_burn_hand(self, config: AZRConfig = None) -> bool:
        """Vérifie si c'est la main de brûlage (main 0)"""
        if config:
            return self.hand_number == config.burn_hand_number
        return self.hand_number == 0  # Fallback

@dataclass  
class BaccaratGame:
    """
    Structure de données pour une partie complète de Baccarat
    
    Fenêtre de 60 manches P/B maximum (2^60 possibilités)
    """
    
    # Identification
    game_number: int
    
    # Initialisation
    burn_cards_count: int               # 2-11 cartes brûlées
    burn_parity: str                    # 'PAIR' ou 'IMPAIR'
    initial_sync_state: str             # 'SYNC' ou 'DESYNC'
    
    # Données de la partie
    hands: List[BaccaratHand] = field(default_factory=list)
    
    # Statistiques
    hand_counter: int = 0               # Compteur de mains (0, 1, 2, 3, ...)
    total_hands: int = 0                # Toutes les mains (incluant TIE)
    pb_hands: int = 0                   # Manches P/B seulement
    tie_hands: int = 0                  # TIE seulement
    
    # État actuel
    current_sync_state: str = 'SYNC'
    last_pb_result: Optional[str] = None
    
    def add_hand(self, hand: BaccaratHand, config: AZRConfig = None):
        """Ajoute une main à la partie avec mise à jour des statistiques"""
        self.hands.append(hand)

        # Incrémenter le compteur de mains seulement pour les vraies mains (pas le brûlage)
        if not hand.is_burn_hand(config):
            self.hand_counter += 1
            self.total_hands += 1

            if config:
                if hand.result in config.pb_results:
                    self.pb_hands += 1
                elif hand.result == config.game_results[2]:  # 'TIE'
                    self.tie_hands += 1
            else:
                # Fallback
                if hand.result in ['PLAYER', 'BANKER']:
                    self.pb_hands += 1
                elif hand.result == 'TIE':
                    self.tie_hands += 1
    
    def is_complete(self, config: AZRConfig = None) -> bool:
        """Vérifie si la partie est complète (60 manches P/B)"""
        max_pb_hands = config.max_manches_per_game if config else 60
        return self.pb_hands >= max_pb_hands
    
    def get_pb_sequence(self, config: AZRConfig = None) -> List[str]:
        """Retourne la séquence P/B (sans TIE)"""
        pb_results = config.pb_results if config else ['PLAYER', 'BANKER']
        return [hand.result for hand in self.hands if hand.result in pb_results]
    


    def get_sync_pattern(self) -> List[str]:
        """
        Pattern SYNC/DESYNC de toutes les mains
        """
        return [hand.sync_state for hand in self.hands]



    def get_parity_pattern(self) -> List[str]:
        """
        Pattern parité cartes de toutes les mains
        """
        return [hand.cards_parity for hand in self.hands]

    def get_game_summary(self) -> Dict[str, Any]:
        """
        Résumé complet de la partie pour affichage
        """
        return {
            # Séquences de base
            'pb_sequence': self.get_pb_sequence(),
            'sync_pattern': self.get_sync_pattern(),
            'parity_pattern': self.get_parity_pattern(),

            # Statistiques
            'hand_counter': self.hand_counter,
            'total_hands': self.total_hands,
            'pb_hands': self.pb_hands,
            'tie_hands': self.tie_hands,
            'last_pb_result': self.last_pb_result,
            'current_sync_state': self.current_sync_state
        }

################################################################################
#                                                                              #
#  ⚙️ SECTION 3 : MOTEUR DE COMPTAGE OPÉRATIONNEL                              #
#                                                                              #
################################################################################

class BaccaratCountingEngine:
    """
    Moteur de comptage conforme à systeme_comptage_baccarat_complet.txt

    Calcule les 4 INDEX de comptage + INDEX 6 combiné pour chaque main
    OPÉRATIONNEL et TESTÉ
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.CountingEngine")

    def calculate_cards_distributed(self, total_cards: int, cards_category: str = None) -> Tuple[int, str, str]:
        """
        Calcule INDEX 1 : nombre de cartes distribuées et catégorie

        Args:
            total_cards: Nombre total de cartes distribuées (4, 5, ou 6)
            cards_category: Catégorie pré-calculée (optionnel)

        Returns:
            Tuple[total_cards, parity, category]
        """
        if total_cards % 2 == 0:
            parity = self.config.parities[0]  # 'PAIR'
            if total_cards in self.config.card_counts['pair_values']:
                # Utiliser le mapping de configuration
                category = self.config.card_mapping.get(total_cards, f'pair_{total_cards}')
            else:
                # Cas exceptionnels
                category = f'pair_{total_cards}'
        else:
            parity = self.config.parities[1]  # 'IMPAIR'
            if total_cards in self.config.card_counts['impair_values']:
                # Utiliser le mapping de configuration
                category = self.config.card_mapping.get(total_cards, f'impair_{total_cards}')
            else:
                # Cas exceptionnels
                category = f'impair_{total_cards}'

        # Utiliser la catégorie pré-calculée si fournie
        if cards_category:
            category = cards_category

        return total_cards, parity, category

    def create_burn_hand(self, burn_cards_count: int, burn_parity: str, initial_sync_state: str) -> BaccaratHand:
        """
        Crée la main 0 (brûlage) avec seulement INDEX 1 renseigné

        Args:
            burn_cards_count: Nombre de cartes brûlées (2-11)
            burn_parity: Parité du brûlage ('PAIR' ou 'IMPAIR')
            initial_sync_state: État initial ('SYNC' ou 'DESYNC')

        Returns:
            BaccaratHand: Main 0 avec INDEX 1 seulement
        """
        # Créer la main 0 (brûlage)
        burn_hand = BaccaratHand(
            hand_number=self.config.burn_hand_number,        # Main 0
            pb_hand_number=self.config.burn_pb_hand_number,  # Manche 0 (brûlage)
            sync_state=initial_sync_state,                   # INDEX 1 : État initial
            cards_distributed=burn_cards_count,              # Nombre de cartes brûlées
            cards_parity=self.config.defaults['empty_string'],    # INDEX 2 : Vide pour brûlage
            cards_category=self.config.defaults['empty_string'],  # INDEX 2 : Vide pour brûlage
            result=self.config.defaults['empty_string']           # INDEX 3 : Vide
        )

        self.logger.debug(f"Main de brûlage créée: INDEX1={initial_sync_state}")
        return burn_hand

    def calculate_sync_state(self, current_sync_state: str, cards_parity: str) -> str:
        """
        Calcule INDEX 2 : nouvel état SYNC/DESYNC

        LOGIQUE :
        - Nombre PAIR de cartes → CONSERVE l'état
        - Nombre IMPAIR de cartes → CHANGE l'état
        """
        if cards_parity == self.config.parities[0]:  # 'PAIR'
            # PAIR conserve l'état
            return current_sync_state
        else:
            # IMPAIR change l'état
            return self.config.sync_states[1] if current_sync_state == self.config.sync_states[0] else self.config.sync_states[0]



    def process_hand(self, game: BaccaratGame, result: str,
                    total_cards: int, cards_category: str) -> BaccaratHand:
        """
        Traite une main complète et calcule tous les index

        Args:
            game: Partie en cours
            result: 'PLAYER', 'BANKER', 'TIE'
            total_cards: Nombre total de cartes distribuées (4, 5, ou 6)
            cards_category: Catégorie pré-calculée ('pair_4', 'impair_5', 'pair_6')
        """
        # INDEX 2 : Comptage cartes
        total_cards, cards_parity, cards_category = self.calculate_cards_distributed(
            total_cards, cards_category
        )

        # INDEX 1 : État SYNC/DESYNC au DÉBUT de cette main (état actuel)
        current_sync_state = game.current_sync_state

        # Calculer le NOUVEL état pour la PROCHAINE main
        next_sync_state = self.calculate_sync_state(
            game.current_sync_state, cards_parity
        )



        # Numéro de manche selon la logique correcte
        pb_hand_number = None
        if result in self.config.pb_results:
            # P/B : incrémente le compteur de manches
            pb_hand_number = game.pb_hands + 1
        elif result == self.config.game_results[2]:  # 'TIE'
            # TIE : garde le numéro de la dernière manche P/B (MÊME manche)
            if game.pb_hands > 0:
                pb_hand_number = game.pb_hands  # MÊME numéro que la dernière manche P/B
            else:
                pb_hand_number = self.config.burn_pb_hand_number  # Si pas encore de P/B, reste à 0 (comme brûlage)

        # Créer la main (numérotation commence à 1 car main 0 = brûlage)
        # Si main 0 (brûlage) existe, les mains suivantes sont 1, 2, 3...
        hand_number = len(game.hands)  # game.hands[0] = brûlage, donc len = 1 → main 1

        # INDEX 3 : Gestion du résultat
        result_index3 = result  # INDEX 3 contient toujours le résultat (PLAYER/BANKER/TIE)

        hand = BaccaratHand(
            hand_number=hand_number,
            pb_hand_number=pb_hand_number,
            sync_state=current_sync_state,    # INDEX 1 : État au DÉBUT de cette main
            cards_distributed=total_cards,
            cards_parity=cards_parity,
            cards_category=cards_category,
            result=result_index3
        )

        # Mettre à jour l'état du jeu pour la PROCHAINE main
        game.current_sync_state = next_sync_state
        if result in self.config.pb_results:
            game.last_pb_result = result

        # Ajouter la main au jeu
        game.add_hand(hand, self.config)



        return hand

################################################################################
#                                                                              #
#  🎮 SECTION 4 : INTERFACE GRAPHIQUE OPÉRATIONNELLE                          #
#                                                                              #
################################################################################

class BaccaratInterface:
    """
    Interface graphique avec 9 boutons (3 résultats × 3 nombres de cartes)
    Version simplifiée sans prédictions
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.Interface")

        # Composants système
        self.counting_engine = BaccaratCountingEngine(config)

        # État de l'interface
        self.current_game = None
        self.burn_initialized = False

        # Interface graphique
        self.root = tk.Tk()
        self.root.title(self.config.labels['window_title'])
        self.root.geometry(self.config.window_geometry)

        # Variables d'affichage
        self.game_stats_var = tk.StringVar(value=self.config.defaults['game_stats'])

        self._create_interface()

        self.logger.info("Interface graphique initialisée")

    def _create_interface(self):
        """Crée l'interface graphique complète"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding=self.config.paddings['main_frame'])
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Section initialisation brûlage
        self._create_burn_section(main_frame)

        # Section statut de la partie
        self._create_game_status_display(main_frame)

        # Section 9 boutons (3×3)
        self._create_nine_buttons_section(main_frame)

        # Section contrôles
        self._create_controls_section(main_frame)

        # Section statistiques
        self._create_stats_section(main_frame)

    def _create_burn_section(self, parent):
        """Crée la section d'initialisation du brûlage"""
        burn_frame = ttk.LabelFrame(parent, text=self.config.section_titles['burn'],
                                   padding=self.config.paddings['section'])
        burn_frame.pack(fill=tk.X, pady=self.config.paddings['section_y'])

        ttk.Label(burn_frame, text=self.config.labels['burn'],
                 font=self.config.fonts['title']).pack(side=tk.LEFT)

        # Seulement 2 boutons : PAIR et IMPAIR
        buttons_frame = ttk.Frame(burn_frame)
        buttons_frame.pack(side=tk.LEFT, padx=self.config.paddings['burn_buttons'])

        # Bouton PAIR
        pair_btn = tk.Button(buttons_frame, text=self.config.button_texts['pair'],
                            font=self.config.fonts['burn_button'],
                            bg=self.config.colors['burn_bg'],
                            fg=self.config.colors['burn_fg'],
                            width=self.config.button_sizes['burn_width'],
                            height=self.config.button_sizes['burn_height'],
                            command=lambda: self._initialize_burn(self.config.parities[0]))
        pair_btn.pack(side=tk.LEFT, padx=self.config.paddings['burn_button'])

        # Bouton IMPAIR
        impair_btn = tk.Button(buttons_frame, text=self.config.button_texts['impair'],
                              font=self.config.fonts['burn_button'],
                              bg=self.config.colors['burn_bg'],
                              fg=self.config.colors['burn_fg'],
                              width=self.config.button_sizes['burn_width'],
                              height=self.config.button_sizes['burn_height'],
                              command=lambda: self._initialize_burn(self.config.parities[1]))
        impair_btn.pack(side=tk.LEFT, padx=self.config.paddings['burn_button'])

    def _create_game_status_display(self, parent):
        """Crée la section d'affichage du statut de la partie"""
        status_frame = ttk.LabelFrame(parent, text=self.config.section_titles['status'],
                                     padding=self.config.paddings['section'])
        status_frame.pack(fill=tk.X, pady=self.config.paddings['section_y'])

        # Frame principal pour organiser gauche/droite
        main_status_frame = ttk.Frame(status_frame)
        main_status_frame.pack(fill=tk.X)

        # Frame gauche pour les statistiques
        stats_frame = ttk.Frame(main_status_frame)
        stats_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Frame pour les statistiques avec formatage mixte
        self.stats_display_frame = ttk.Frame(stats_frame)
        self.stats_display_frame.pack(side=tk.LEFT)

        # Labels séparés pour formatage différent
        self.manche_label = ttk.Label(self.stats_display_frame, text=self.config.labels['manche'],
                                     font=self.config.fonts['normal'])
        self.manche_label.pack(side=tk.LEFT)

        self.manche_numbers = ttk.Label(self.stats_display_frame, text=self.config.defaults['manche_display'],
                                       font=self.config.fonts['title'])
        self.manche_numbers.pack(side=tk.LEFT)

        self.other_stats = ttk.Label(self.stats_display_frame, text=self.config.defaults['empty_string'],
                                    font=self.config.fonts['normal'])
        self.other_stats.pack(side=tk.LEFT)

        # Frame droite pour les boutons de contrôle
        control_buttons_frame = ttk.Frame(main_status_frame)
        control_buttons_frame.pack(side=tk.RIGHT)

        # Bouton Réinitialiser (en haut)
        reset_btn = tk.Button(control_buttons_frame,
                             text=self.config.button_texts['reset_game'],
                             font=self.config.fonts['normal'],
                             width=self.config.button_sizes['control_width'],
                             height=self.config.button_sizes['control_height'],
                             command=self._reset_game)
        reset_btn.pack(pady=(0, 2))

        # Bouton Annuler Main (en bas)
        undo_btn = tk.Button(control_buttons_frame,
                            text=self.config.button_texts['undo_hand'],
                            font=self.config.fonts['normal'],
                            width=self.config.button_sizes['control_width'],
                            height=self.config.button_sizes['control_height'],
                            command=self._undo_last_hand)
        undo_btn.pack()

    def _create_nine_buttons_section(self, parent):
        """
        Crée la section des 9 boutons (3 résultats × 3 nombres de cartes)
        AMÉLIORATIONS INTERFACE :
        - Suppression des étiquettes headers inutiles
        - Réduction taille boutons par 2
        - Police plus claire pour meilleure lisibilité
        """
        buttons_frame = ttk.LabelFrame(parent, text=self.config.section_titles['buttons'],
                                      padding=self.config.paddings['section'])
        buttons_frame.pack(fill=tk.BOTH, expand=True, pady=self.config.paddings['section_y'])

        # Configuration grid
        for i in range(self.config.widget_sizes['grid_columns']):
            buttons_frame.columnconfigure(i, weight=1)

        # Boutons pour chaque combinaison (résultat + nombre de cartes)
        headers = self.config.game_results
        colors = [self.config.colors['player'], self.config.colors['banker'], self.config.colors['tie']]
        card_counts = [
            (4, self.config.card_descriptions[4]),
            (5, self.config.card_descriptions[5]),
            (6, self.config.card_descriptions[6])
        ]

        for row, (total_cards, card_desc) in enumerate(card_counts, start=0):
            for col, (result, color) in enumerate(zip(headers, colors)):
                btn_text = f"{result} {total_cards}\n({card_desc})"

                btn = tk.Button(buttons_frame, text=btn_text,
                              font=self.config.fonts['game_button'],
                              bg=color, fg=self.config.colors['text_light'],
                              relief="raised", bd=self.config.button_sizes['game_bd'],
                              height=self.config.button_sizes['game_height'],
                              width=self.config.button_sizes['game_width'],
                              command=lambda r=result, c=total_cards: self._process_hand(r, c))
                btn.grid(row=row, column=col, sticky="ew",
                        padx=self.config.paddings['game_button'],
                        pady=self.config.paddings['game_button'])

    def _create_controls_section(self, parent):
        """Crée la section des contrôles"""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, pady=self.config.paddings['section_y'])

        # Boutons de contrôle
        ttk.Button(controls_frame, text=self.config.button_texts['save'],
                  command=self._save_game).pack(side=tk.LEFT, padx=(0, self.config.paddings['control_button']))

        ttk.Button(controls_frame, text=self.config.button_texts['new_game'],
                  command=self._new_game).pack(side=tk.LEFT, padx=self.config.paddings['control_button'])

        ttk.Button(controls_frame, text=self.config.button_texts['statistics'],
                  command=self._show_statistics).pack(side=tk.LEFT, padx=self.config.paddings['control_button'])

        ttk.Button(controls_frame, text=self.config.button_texts['quit'],
                  command=self._quit_application).pack(side=tk.RIGHT)

    def _create_stats_section(self, parent):
        """Crée la section des statistiques en temps réel"""
        stats_frame = ttk.LabelFrame(parent, text=self.config.section_titles['stats'],
                                    padding=self.config.paddings['section'])
        stats_frame.pack(fill=tk.X)

        # Créer un Text widget pour affichage des stats
        self.stats_text = tk.Text(stats_frame,
                                 height=self.config.widget_sizes['stats_height'],
                                 width=self.config.widget_sizes['stats_width'],
                                 font=self.config.fonts['stats'], state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stats_text.config(yscrollcommand=scrollbar.set)

    def _initialize_burn(self, parity: str):
        """Initialise le brûlage avec la parité ET crée la main 0"""
        if self.burn_initialized:
            messagebox.showwarning("Attention", self.config.messages['burn_already_init'])
            return

        # Déterminer état initial et nombre de cartes par défaut
        initial_sync_state = self.config.initial_sync_mapping[parity]
        burn_cards_count = self.config.burn_defaults[parity]

        # Créer nouvelle partie (initialisation)
        self.current_game = BaccaratGame(
            game_number=self.config.first_game_number,
            burn_cards_count=burn_cards_count,
            burn_parity=parity,
            initial_sync_state=initial_sync_state,
            current_sync_state=initial_sync_state
        )

        # Créer la main 0 (brûlage) avec seulement INDEX 1
        burn_hand = self.counting_engine.create_burn_hand(
            burn_cards_count, parity, initial_sync_state
        )

        # Ajouter la main 0 au jeu
        self.current_game.add_hand(burn_hand, self.config)

        self.burn_initialized = True

        # Mettre à jour affichage
        self._update_display()

        self.logger.info(f"Brûlage initialisé: parité {parity} -> {initial_sync_state}")
        self.logger.info(f"Brûlage créé: {burn_hand.sync_state}")
        # Pas de popup - l'utilisateur voit directement l'état dans l'interface

    def _process_hand(self, result: str, total_cards: int):
        """
        Traite une main avec résultat et nombre total de cartes

        Args:
            result: 'PLAYER', 'BANKER', 'TIE' (le gagnant de la main)
            total_cards: 4, 5, ou 6 (nombre total de cartes distribuées dans la main)
        """
        if not self.burn_initialized:
            messagebox.showwarning("Attention", self.config.messages['burn_first'])
            return

        if self.current_game.is_complete(self.config):
            messagebox.showinfo("Partie Terminée",
                              self.config.messages['game_complete'].format(self.config.max_manches_per_game))
            return

        try:
            # Mapping automatique vers catégorie INDEX 2
            cards_category = self.config.card_mapping.get(total_cards)
            if not cards_category:
                raise ValueError(self.config.messages['invalid_cards'].format(total_cards))

            # Traiter la main avec le moteur de comptage
            # Le moteur calcule automatiquement tous les INDEX
            hand = self.counting_engine.process_hand(
                self.current_game, result, total_cards, cards_category
            )

            # Mettre à jour affichage
            self._update_display()

            # Log de la main traitée avec format regroupé
            # Le numéro de main = hand_counter (qui commence à 1 pour les vraies mains)
            main_number = self.current_game.hand_counter
            self.logger.info(f"Main {main_number} traitée: {result} | État: {hand.sync_state} | INDEX2: {hand.cards_category} | INDEX5: {hand.index5_combined}")

        except Exception as e:
            self.logger.error(f"Erreur traitement main: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du traitement: {e}")

    def _update_display(self):
        """Met à jour l'affichage de l'interface"""
        if not self.current_game:
            return

        try:
            # Mettre à jour statistiques de partie avec formatage personnalisé
            self.manche_numbers.config(text=f"{self.current_game.pb_hands} / {self.config.max_manches_per_game}")
            self.other_stats.config(text=f" | Main: {self.current_game.hand_counter} | TIE: {self.current_game.tie_hands}")

            # Mettre à jour les statistiques détaillées (avec protection)
            self.root.after_idle(self._update_stats_display)
        except Exception as e:
            self.logger.error(f"Erreur mise à jour affichage: {e}")

    def _update_stats_display(self):
        """Met à jour l'affichage des statistiques détaillées"""
        if not self.current_game:
            return

        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)

        # Affichage système de comptage
        self.stats_text.insert(tk.END, f"⚙️ SYSTÈME DE COMPTAGE:\n")
        self.stats_text.insert(tk.END, f"  • État SYNC/DESYNC: {self.current_game.current_sync_state}\n")
        self.stats_text.insert(tk.END, f"  • Dernière main P/B: {self.current_game.last_pb_result or self.config.defaults['no_result']}\n")

        if self.current_game.hands:
            last_hand = self.current_game.hands[-1]
            self.stats_text.insert(tk.END, f"\n📋 DERNIÈRE MAIN TRAITÉE:\n")
            na_display = self.config.defaults['na_display']
            self.stats_text.insert(tk.END, f"  • INDEX 1 (SYNC): {last_hand.sync_state}\n")
            self.stats_text.insert(tk.END, f"  • INDEX 2 (cartes): {last_hand.cards_category or na_display}\n")
            self.stats_text.insert(tk.END, f"  • INDEX 3 (résultat): {last_hand.result or na_display}\n")
            self.stats_text.insert(tk.END, f"  • INDEX 5 (INDEX 1+2): {last_hand.index5_combined}\n")

        self.stats_text.insert(tk.END, f"\n📊 SÉQUENCES:\n")
        pb_sequence = self.current_game.get_pb_sequence(self.config)
        sync_pattern = self.current_game.get_sync_pattern()

        limit = self.config.formats['sequence_display_limit']
        no_result = self.config.defaults['no_result']
        self.stats_text.insert(tk.END, f"  • P/B: {' '.join(pb_sequence[-limit:]) if pb_sequence else no_result}\n")
        self.stats_text.insert(tk.END, f"  • SYNC: {' '.join(sync_pattern[-limit:]) if sync_pattern else no_result}\n")

        self.stats_text.config(state=tk.DISABLED)
        self.stats_text.see(tk.END)



    def _save_game(self):
        """Sauvegarde la partie actuelle"""
        if not self.current_game:
            messagebox.showwarning("Attention", self.config.messages['no_game'])
            return

        try:
            # Créer données de sauvegarde
            save_data = {
                'game': {
                    'game_number': self.current_game.game_number,
                    'burn_cards_count': self.current_game.burn_cards_count,
                    'burn_parity': self.current_game.burn_parity,
                    'initial_sync_state': self.current_game.initial_sync_state,
                    'hands': [
                        {
                            'hand_number': hand.hand_number,
                            'pb_hand_number': hand.pb_hand_number,
                            'cards_distributed': hand.cards_distributed,
                            'cards_parity': hand.cards_parity,
                            'cards_category': hand.cards_category,
                            'sync_state': hand.sync_state,
                            'result': hand.result,
                            'timestamp': hand.timestamp.isoformat()
                        }
                        for hand in self.current_game.hands
                    ]
                },
                'save_timestamp': datetime.now().isoformat()
            }

            # Sauvegarder dans fichier JSON
            filename = f"{self.config.files['save_prefix']}{datetime.now().strftime(self.config.formats['datetime_file'])}{self.config.files['save_extension']}"
            with open(filename, 'w', encoding=self.config.files['encoding']) as f:
                json.dump(save_data, f, indent=self.config.formats['json_indent'], ensure_ascii=False)

            messagebox.showinfo("Sauvegarde", self.config.messages['game_saved'].format(filename))
            self.logger.info(f"Partie sauvegardée: {filename}")

        except Exception as e:
            self.logger.error(f"Erreur sauvegarde: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _new_game(self):
        """Démarre une nouvelle partie"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Nouvelle Partie", self.config.messages['new_game_confirm']):
                return

        # Réinitialiser état
        self.current_game = None
        self.burn_initialized = False

        # Réinitialiser affichage
        self.manche_numbers.config(text=self.config.defaults['manche_display'])
        self.other_stats.config(text=self.config.defaults['empty_stats'])

        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, self.config.messages['new_game_init'])
        self.stats_text.config(state=tk.DISABLED)

        self.logger.info("Nouvelle partie initialisée")

    def _reset_game(self):
        """Réinitialise complètement la partie actuelle"""
        if not messagebox.askyesno("Réinitialiser Partie", self.config.messages['reset_confirm']):
            return

        # Réinitialiser complètement l'état
        self.current_game = None
        self.burn_initialized = False

        # Réinitialiser affichage
        self.manche_numbers.config(text=self.config.defaults['manche_display'])
        self.other_stats.config(text=self.config.defaults['empty_stats'])

        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, self.config.messages['reset_success'])
        self.stats_text.config(state=tk.DISABLED)

        self.logger.info("Partie réinitialisée - tous les compteurs remis à zéro")

    def _undo_last_hand(self):
        """Annule la dernière main saisie (pas le brûlage)"""
        if not self.current_game:
            messagebox.showwarning("Attention", self.config.messages['undo_no_hand'])
            return

        # Vérifier qu'il y a des mains à annuler (plus que le brûlage)
        if len(self.current_game.hands) <= 1:
            if len(self.current_game.hands) == 1 and self.current_game.hands[0].is_burn_hand(self.config):
                messagebox.showwarning("Attention", self.config.messages['undo_only_burn'])
            else:
                messagebox.showwarning("Attention", self.config.messages['undo_no_hand'])
            return

        # Identifier la dernière main (pas le brûlage)
        last_hand = self.current_game.hands[-1]
        if last_hand.is_burn_hand(self.config):
            messagebox.showwarning("Attention", self.config.messages['undo_only_burn'])
            return

        # Demander confirmation
        if not messagebox.askyesno("Annuler Main",
                                  self.config.messages['undo_confirm'].format(last_hand.hand_number)):
            return

        # Sauvegarder les informations avant suppression
        hand_number = last_hand.hand_number
        was_pb_hand = last_hand.is_pb_hand(self.config)
        was_tie_hand = last_hand.is_tie_hand(self.config)

        # SUPPRESSION PROGRESSIVE selon l'ordre spécifié :

        # 1. Supprimer INDEX 1 (État SYNC/DESYNC) - Restaurer l'état pour la main suivante
        self._restore_sync_state()

        # 2. Supprimer INDEX 3 (Résultat) - Effacer le résultat de la main
        last_hand.result = self.config.defaults['empty_string']

        # 3. Supprimer INDEX 2 (Comptage cartes) - Effacer les informations de cartes
        last_hand.cards_category = self.config.defaults['empty_string']
        last_hand.cards_parity = self.config.defaults['empty_string']

        # 4. INDEX 5 est automatiquement mis à jour (propriété calculée)

        # Finalement, supprimer complètement la main de la liste
        self.current_game.hands.pop()

        # Restaurer les compteurs
        if not last_hand.is_burn_hand(self.config):
            self.current_game.hand_counter -= 1
            self.current_game.total_hands -= 1

            if was_pb_hand:
                self.current_game.pb_hands -= 1
                # Restaurer last_pb_result
                self._restore_last_pb_result()
            elif was_tie_hand:
                self.current_game.tie_hands -= 1

        # Mettre à jour l'affichage
        self._update_display()

        # Message de confirmation
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.insert(tk.END, self.config.messages['undo_success'].format(hand_number))
        self.stats_text.config(state=tk.DISABLED)

        self.logger.info(f"Main {hand_number} annulée - état restauré")

    def _restore_last_pb_result(self):
        """Restaure le dernier résultat P/B après annulation"""
        # Chercher la dernière main P/B restante
        for hand in reversed(self.current_game.hands):
            if hand.is_pb_hand(self.config):
                self.current_game.last_pb_result = hand.result
                return
        # Si aucune main P/B trouvée
        self.current_game.last_pb_result = None

    def _restore_sync_state(self):
        """Restaure l'état SYNC/DESYNC après annulation"""
        if len(self.current_game.hands) == 0:
            # Aucune main restante - état par défaut
            self.current_game.current_sync_state = self.config.defaults['default_sync']
        elif len(self.current_game.hands) == 1 and self.current_game.hands[0].is_burn_hand(self.config):
            # Seul le brûlage reste - utiliser son état
            self.current_game.current_sync_state = self.current_game.hands[0].sync_state
        else:
            # Restaurer l'état basé sur la dernière main restante
            last_remaining_hand = self.current_game.hands[-1]
            if last_remaining_hand.is_burn_hand(self.config):
                # Si la dernière main est le brûlage, utiliser son état
                self.current_game.current_sync_state = last_remaining_hand.sync_state
            else:
                # CORRECTION: Calculer l'état que la PROCHAINE main DEVRAIT avoir
                # basé sur la dernière main restante (qui devient la nouvelle "dernière main")
                self.current_game.current_sync_state = self.counting_engine.calculate_sync_state(
                    last_remaining_hand.sync_state, last_remaining_hand.cards_parity
                )

    def _show_statistics(self):
        """Affiche les statistiques détaillées dans une fenêtre séparée"""
        stats_window = tk.Toplevel(self.root)
        stats_window.title(self.config.section_titles['stats_window'])
        stats_window.geometry(f"{self.config.widget_sizes['stats_window_width']}x{self.config.widget_sizes['stats_window_height']}")

        # Text widget avec scrollbar
        text_frame = ttk.Frame(stats_window)
        text_frame.pack(fill=tk.BOTH, expand=True,
                       padx=self.config.paddings['stats_window'],
                       pady=self.config.paddings['stats_window'])

        stats_text = tk.Text(text_frame, font=self.config.fonts['stats_window'])
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=stats_text.yview)

        stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        stats_text.config(yscrollcommand=scrollbar.set)

        # Générer statistiques complètes
        stats_content = "🧠 STATISTIQUES BCT (Baccarat Counting Tool)\n"
        stats_content += self.config.formats['stats_separator'] + "\n\n"

        # Configuration système
        stats_content += "📊 CONFIGURATION SYSTÈME:\n"
        stats_content += f"  • Manches max par partie: {self.config.max_manches_per_game}\n"
        stats_content += f"  • Mains max par partie: {self.config.max_hands_per_game}\n\n"

        # Partie actuelle
        if self.current_game:
            stats_content += f"🎲 PARTIE ACTUELLE:\n"
            stats_content += f"  • Numéro: {self.current_game.game_number}\n"
            stats_content += f"  • Brûlage: parité {self.current_game.burn_parity}\n"
            stats_content += f"  • État initial: {self.current_game.initial_sync_state}\n"
            stats_content += f"  • État actuel: {self.current_game.current_sync_state}\n"
            stats_content += f"  • Manches P/B: {self.current_game.pb_hands}/{self.config.max_manches_per_game}\n"
            stats_content += f"  • Total mains: {self.current_game.total_hands}\n"
            stats_content += f"  • TIE: {self.current_game.tie_hands}\n"


            # Détails des mains
            if self.current_game.hands:
                stats_content += f"📋 DÉTAILS DES MAINS:\n"
                recent_limit = self.config.formats['recent_hands_limit']
                for hand in self.current_game.hands[-recent_limit:]:
                    stats_content += f"  Main {hand.hand_number}: {hand.result} {hand.cards_distributed}c → {hand.index5_combined}\n"
        else:
            stats_content += "🎲 AUCUNE PARTIE EN COURS\n"

        stats_text.insert(tk.END, stats_content)
        stats_text.config(state=tk.DISABLED)

    def _quit_application(self):
        """Quitte l'application proprement"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Quitter", self.config.messages['quit_confirm']):
                return

        self.logger.info("Fermeture de l'application")

        # Fermer interface
        self.root.quit()
        self.root.destroy()

    def run(self):
        """Lance l'interface graphique"""
        self.logger.info("Démarrage de l'interface graphique")

        # Gestionnaire de fermeture
        self.root.protocol("WM_DELETE_WINDOW", self._quit_application)

        # Démarrer boucle principale
        self.root.mainloop()

################################################################################
#                                                                              #
#  🚀 SECTION 5 : FONCTION PRINCIPALE                                         #
#                                                                              #
################################################################################

def main():
    """
    Fonction principale du programme BCT (Baccarat Counting Tool)

    Initialise tous les composants et lance l'interface graphique
    """
    print("🧠 BCT - BACCARAT COUNTING TOOL (Version Refactorisée)")
    print("=" * 60)
    print("Initialisation du système...")

    try:
        # Initialiser configuration
        config = AZRConfig()

        # Reconfigurer le logging avec le fichier de configuration
        setup_logging(config.files['log'])

        print(f"\n📊 Configuration système:")
        print(f"  • Manches max par partie: {config.max_manches_per_game}")
        print(f"  • Mains max par partie: {config.max_hands_per_game}")

        print(f"\n✅ Composants opérationnels:")
        print(f"  • Configuration centralisée")
        print(f"  • Système de comptage conforme")
        print(f"  • Interface graphique simplifiée")
        print(f"  • Gestion des données de partie")

        # Initialiser et lancer interface
        print("\n🎮 Lancement de l'interface graphique...")
        interface = BaccaratInterface(config)
        interface.run()

    except KeyboardInterrupt:
        print("\n⚠️ Interruption utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        logger.error(f"Erreur fatale: {e}", exc_info=True)
    finally:
        print("\n👋 Arrêt du programme")

if __name__ == "__main__":
    main()
